
Constraint Solving
===================

<PERSON>: modeling and solving constraints.


Let x be a column vector for position.  {has shape} :: (Np, 1)
Let v be a column vector for velocity.              :: (Np, 1)

Position constraint:  C(x)  = 0
Velocity constraint:  C'(v) = 0   (obtain by differentiating C)

Velocity contraints are usually linear.
Thus, we write C using a matrix, /the Jacobian/.
The Jacobian J converts from velocity space into constraint space.
For a single scalar constraint (e.g. distance) the Jacobian has 1 row.
For multiple constraints, the Jacobian has one row per constraint.

  C'(v) = J*V = 0

The velocity V here is a column vector which contains all of the velocities that are 
relevant to the constraint -- e.g. the velocities and rotations of both rigid bodies.  


To satisfy the contraint, each row of J must be perpendicular to V.
E.g. the column vectors of J^T are all perpendicular to V.
Perpendicular forces do no work, so we use J^T to compute the constraint force Fc.
The Lagrange multiplier \lambda is the force in constraint space.
For a scalar constraint, \lambda is a scalar -- e.g. tension or contact force.
J^T converts from constraint space back to velocity/position/force space.

  Fc = J^T . \lambda

Define the effective mass of the constraint (mc):

  Mc = 1 / (J * M^{-1} * J^T)

Solve for \lambda in terms of v', which is our -candidate- velocity.

  \lambda = Mc * -(J * v') / dt   { force = mass * acceleration }

--- Derivation ---

From Newton's law:

  F = MA
  F = M * (dV / dt)   (dV is delta-V)
  dV = M^-1 * F * dt

Assume we are given v1, which is the current set of velocities, that break constraints.
We calculate v2, the velocities at the next time step, as:

  v2 = v1 + dV
     = v1 + M^-1 * F * dt
     = v1 + M^-1 * J^T * \lambda * dt

We solve for \lambda, such that v2 will solve our constraints.
The first step is to project both sides of the equation back into constraint space.

  J * v2  = J * (v1 + M^-1 * (J^T * \lambda) * dt)    =  0
          = J * v1  +  J * M^-1 * J^T * \lambda * dt  =  0
  \lambda = -(J * v1)/dt  *  1/(J * M^-1 * J^T)


--- Example ---

derivative of magnitude of a vector:  d|x| / dt = x/|x| * dx/dt


Position constraint:  |x1 - x2| - D = 0
Velocity constraint:  d . (v1 - v2) = 0
  Where d = (x1 - x2) / |x1 - x2|    (from derivative of |x1 - x2|, which involves sqrt)

J  = [d^T , -d^T]                    (remember: d^T * v1 = d . v1)
Mc = 1 / (1/m1 + 1/m2)


Expanding to rotation.
Assume x1,x2 are CGs, r1,r2 are vectors off of CG, and w1,w2 are rotation vectors (rot speed * axis).
dx1/dt = v1
dr1/dt = w1 \cross r1   (note that r1 is the vector in world-space)

Position constraint:  |p1 - p2| = 0,  where p1 = x1 + r1
Velocity constraint:  d . (v1 + (w1 \cross r1) - v2 - (w2 \cross r2)) = 0
  Where d = (p1 - p2) / |p1 - p2|  

J = [ d^T , (r1 \cross d)^T , -d^T , (r2 \cross d)^T ]

Derivation:   d . (w1 \cross r1)  =  w1 . (r1 \cross d)