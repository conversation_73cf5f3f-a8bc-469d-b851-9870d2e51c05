
use rustwin::{
    graphics::{
        camera::CameraState,
        gpu_buffer::CpuVertex,
        render::triangle_render_pipeline,
        scene::{Body, RenderableBody, Scene},
        window::WindowState,
        window_app::{SceneState, run_application},
    },
    simulation::{
        storage::{Handle, StoragePoolSlot},
        creature::{Cell, Creature, Joint, World},
    }
};

use nalgebra::{Point2, Point3, Vector3};


const TRUNK: &[CpuVertex] = &[
    CpuVertex {
        position: Point3::<f32>::new(-0.1, -0.5, 0.0),
        color: Point3::<f32>::new(0.3, 0.2, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(0.1, -0.5, 0.0),
        color: Point3::<f32>::new(0.2, 0.1, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(0.1, 0.5, 0.0),
        color: Point3::<f32>::new(0.3, 0.1, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(-0.1, 0.5, 0.0),
        color: Point3::<f32>::new(0.2, 0.1, 0.0)
    },
];


fn make_trunk(p: Point3<f32>, angle: f32, scale: f32) -> Body {
    let mut b = Body::new(p, &TRUNK, &[0, 1, 2, 2, 3, 0]);
    b.rotation = angle;
    b.scale = scale;
    return b;
}


fn build_world(world: &mut World) {
    let root_h = world.add_cell(Cell {
        body: make_trunk(Point3::new(0.0, -0.5, 0.0), 0.0, 0.02),
        joints: Vec::new()
    });
    world.add_creature(Creature {
        root_cell: root_h
    });
}


fn update_world(world: &mut World) {
    if world.cells.len() > 512 {
        return
    }

    // List of new branches to split.
    let mut splits: Vec<Handle<Cell>> = Vec::new();

    // Increase the size of all existing cells, and find the ones that need to be split.
    // We cannot add cells yet without invalidating the iterator.
    for (i, cell) in world.cells.items.iter().enumerate() {
        let njoints = cell.borrow_mut().joints.len();
        let body = &mut cell.borrow_mut().body;
        body.scale *= 1.005;
        if (body.scale > 0.025) && (njoints == 0) {
            splits.push(Handle::<Cell>::new(i));
        }
    }

    // Split the cells that have grown big enough.
    while !splits.is_empty() {
        // Get the parent scale.
        let parent_h = splits.pop().unwrap();
        let child_scale: f32;
        {
            let parent_cell = world.cells.get_mut(parent_h);
            child_scale = parent_cell.body.scale * 0.7;
        }

        // Add two new branches.  (This could invalidate the previous parent_cell ref).
        let branch_h1 = world.add_cell(Cell {
            body: make_trunk(Point3::new(0.0, 0.0, 0.0), 0.0, child_scale),
            joints: Vec::new()
        });
        let branch_h2 = world.add_cell(Cell {
            body: make_trunk(Point3::new(0.0, 0.0, 0.0), 0.0, child_scale),
            joints: Vec::new()
        });

        // Connect the branches to the parent.
        let parent_cell = world.cells.get_mut(parent_h);
        let parent_offset = Vector3::new(0.0, 0.5, 0.0);
        let child_offset = Vector3::new(0.0, -0.5, 0.0);
        parent_cell.joints.push(Joint {
            parent: parent_h,
            child: branch_h1,
            parent_offset: parent_offset,
            child_offset: child_offset,
            angle: 0.75
        });
        parent_cell.joints.push(Joint {
            parent: parent_h,
            child: branch_h2,
            parent_offset: parent_offset,
            child_offset: child_offset,
            angle: -0.75
        });
    }

    // Update positions and rotations of all branches.
    world.update();
}


struct WorldRenderer {
    world: World,
    scene: Scene<StoragePoolSlot<Cell>>,
    camera_state: CameraState,
    render_pipeline: wgpu::RenderPipeline,
    current_selector_point: Option<Point2<f32>>,
}

impl SceneState for WorldRenderer {
    fn new(wstate: &WindowState) -> Self {
        let mut camera_state = CameraState::new(&wstate.device);
        camera_state.set_aspect_ratio(wstate.size.width, wstate.size.height);

        let render_pipeline = triangle_render_pipeline(
            wstate,
            &camera_state
        );

        let mut world = World::new();
        build_world(&mut world);

        let scene = Scene::new(&wstate.device);

        return Self {
            world,
            scene,
            camera_state,
            render_pipeline,
            current_selector_point: None,
        }
    }

    fn get_camera_state(&mut self) -> &mut CameraState {
        return &mut self.camera_state;
    }

    fn prepare_to_render(&mut self, queue: &mut wgpu::Queue) {
        update_world(&mut self.world);

        let rbodies = self.world.cells.items.as_mut_slice();
        match self.current_selector_point {
            Some(p) => self.scene.select(rbodies, p),
            None => {}
        }
        self.scene.update_buffer(rbodies, queue);
    }

    fn render(&self, render_pass: &mut wgpu::RenderPass) {
        render_pass.set_pipeline(&self.render_pipeline);
        render_pass.set_bind_group(0, &self.camera_state.bind_group, &[]);
        self.scene.render(render_pass);
    }

    fn mouse_moved(&mut self, x: f32, y: f32) {
        // Convert screen coordinates to world coordinates using stored window dimensions
        let world_point =
            self.camera_state.camera.screen_to_world(x as f32, y as f32).xy();
        self.current_selector_point = Some(world_point);
        // println!("mouse_moved {:?}", world_point);
    }
}


fn main() {
    run_application::<WorldRenderer>();
}
