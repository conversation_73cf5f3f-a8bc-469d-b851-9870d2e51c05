// Defines the scene graph for a 2D or 3D world.

use crate::graphics::{
    geometry::{BoundingBox, point_in_triangle},
    gpu_buffer::CpuVertex,
    render::TriangleList,
};

use nalgebra::{
    Matrix4, Point2, Point3, Similarity3, Translation3, UnitQuaternion, Vector3, Vector4
};

use std::marker::PhantomData;


pub const MAX_VERTICES: usize = 10240;
pub const MAX_INDICES: usize = 10240;


#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>)]
pub struct Body {
    pub position: Point3<f32>,
    pub rotation: f32,
    pub scale: f32,
    pub cpu_vertices: Vec<CpuVertex>,
    pub cpu_indices: Vec<u16>,
    pub view_matrix: Matrix4<f32>,
    pub v_offset: usize,
    pub i_offset: usize,
    transformed_vertices: Vec<CpuVertex>,
    bounding_box: BoundingBox
}


impl Body {
    pub fn new(position: Point3<f32>, cpu_vertices: &[CpuVertex], cpu_indices: &[u16]) -> Body {
        return Body {
            position,
            rotation: 0.0,
            scale: 1.0,
            cpu_vertices: cpu_vertices.to_vec(),
            cpu_indices: cpu_indices.to_vec(),
            view_matrix: Matrix4::identity(),
            v_offset: 0,
            i_offset: 0,
            transformed_vertices: vec!(CpuVertex::default(); cpu_vertices.len()),
            bounding_box: BoundingBox::new()
        };
    }

    // Recalculate the view matrix and transformed vertices.
    pub fn update_view_matrix(&mut self) {
        self.view_matrix = get_view_matrix(&self.position, self.rotation, self.scale);
        self.transform_vertices();
    }

    // Recalculate all of the transformed vertices from the current view matrix.
    pub fn transform_vertices(&mut self) {
        // TODO: this makes lots of copies:
        // 1: transform_vertices:     calc vertices, and write to cpu_vertices
        // 2: write_to_triangle_list: copy to a contiguous TriangleList
        // 3: Scene.update_buffer:    make another copy, before sending to GPU

        let numv = self.cpu_vertices.len();
        for i in 0..numv {
            let hpos = self.cpu_vertices[i].position.to_homogeneous();
            let npos = self.view_matrix * hpos;
            self.transformed_vertices[i] = CpuVertex {
                position: npos.xyz().into(),
                color: self.cpu_vertices[i].color
            };
        }
        self.bounding_box = BoundingBox::from_vertices(self.transformed_vertices.as_slice());
    }

    // Write the transformed vector coordinates to the given TriangleList.
    // Returns pair of (num_vertices, num_indices).
    pub fn write_to_triangle_list(&self, tlist: &mut TriangleList, is_selected: bool) -> (usize, usize) {
        let v_offset = self.v_offset;
        let i_offset = self.i_offset;
        let numv = self.cpu_vertices.len();
        let numi = self.cpu_indices.len();

        for i in 0..numv {
            tlist.set(v_offset + i, self.transformed_vertices[i]);
            // TODO: this is a hack to set the color of the selected triangle.
            if is_selected {
                tlist.cpu_vertex_buffer[v_offset + i].color = [1.0, 1.0, 1.0];
            }
        }
        for j in 0..numi {
            tlist.set_index(i_offset + j, (v_offset as u16) + self.cpu_indices[j]);
        }
        return (numv, numi);
    }

    // Return true if the point p is inside this body.
    pub fn inside(&self, p: &Point3<f32>) -> bool {
        if !self.bounding_box.inside(p) {
            return false;
        }
        let ntri = self.cpu_indices.len() / 3;
        for i in 0..ntri {
            let n = i*3;
            let ai = self.cpu_indices[n] as usize;
            let bi = self.cpu_indices[n+1] as usize;
            let ci = self.cpu_indices[n+2] as usize;
            let a = self.transformed_vertices[ai].position.xy();
            let b = self.transformed_vertices[bi].position.xy();
            let c = self.transformed_vertices[ci].position.xy();
            if point_in_triangle(p.xy(), a, b, c) {
                return true;
            }
        }
        return false;
    }
}


pub trait RenderableBody {
    fn is_visible(&self) -> bool;
    fn get_body(&self) -> &Body;
    fn get_body_mut(&mut self) -> &mut Body;
}

impl RenderableBody for Body {
    #[inline(always)]
    fn is_visible(&self) -> bool {
        return true;
    }

    #[inline(always)]
    fn get_body(&self) -> &Body {
        return self
    }

    #[inline(always)]
    fn get_body_mut(&mut self) -> &mut Body {
        return self;
    }
}


/// A Scene consists of a set of bodies: objects which implement RenderableBody.
/// The actual bodies are not stored inside the scene.
pub struct Scene<BT: RenderableBody> {
    triangles: TriangleList,
    selected: Option<usize>,
    phantom: PhantomData<BT>,
}

impl<BT: RenderableBody> Scene<BT> {
    /// Create a new Scene.
    pub fn new(device: &wgpu::Device) -> Scene<BT> {
        let triangles = TriangleList::new(device, MAX_VERTICES, MAX_INDICES);
        return Scene {
            triangles,
            selected: None,
            phantom: PhantomData
        }
    }

    /// Set the selected body, if the pointer happens to be inside one.
    pub fn select(&mut self, rbodies: &mut [BT], p: Point2<f32>) {
        let p3 = Point3::<f32>::new(p.x, p.y, 0.5);
        self.selected = None;
        for i in 0..rbodies.len() {
            if !rbodies[i].is_visible() {
                continue;
            }
            if rbodies[i].get_body().inside(&p3) {
                self.selected = Some(i)
            }
        }
    }

    /// Transform and copy the vertices from all bodies to the triangle list.
    pub fn update_buffer(&mut self, rbodies: &mut [BT], queue: &mut wgpu::Queue) {
        let mut v_offset: usize = 0;
        let mut i_offset: usize = 0;
        for i in 0..rbodies.len() {
            if !rbodies[i].is_visible() {
                continue;
            }
            let is_selected = match self.selected {
                Some(j) => i == j,
                None => false
            };
            let body = rbodies[i].get_body_mut();

            body.v_offset = v_offset;
            body.i_offset = i_offset;
            let (v_out, i_out) =
                body.write_to_triangle_list(&mut self.triangles, is_selected);
            v_offset += v_out;
            i_offset += i_out;
        }
        self.triangles.num_vertices = v_offset;
        self.triangles.num_indices = i_offset;

        // Copy triangle data to the GPU.
        self.triangles.update_buffer(queue);
    }

    /// Render the scene, by rendering the triangle list.
    pub fn render(&self, render_pass: &mut wgpu::RenderPass) {
        self.triangles.render(render_pass);
    }
}


/// Get a 4x4 transformation matrix with the given translation, rotation, and scale
pub fn get_view_matrix(translation: &Point3<f32>, rotation: f32, scale: f32) -> Matrix4<f32> {
    let trans = Translation3::from(translation.clone());
    let rot = UnitQuaternion::from_axis_angle(&Vector3::z_axis(), rotation);
    let sim = Similarity3::from_parts(trans, rot, scale);
    return sim.to_homogeneous();
}

/// Convert a vector to homogenous coordinates by appending 1, as if it were a point.
/// Note that the built-in to_homogenous() method will append a 0 instead.
#[inline(always)]
pub fn to_homogenous_point(v: &Vector3<f32>) -> Vector4<f32> {
    return Vector4::new(v.x, v.y, v.z, 1.0);
}

/// Transform a point.
#[inline(always)]
pub fn transform_point(m: &Matrix4<f32>, p: &Point3<f32>) -> Point3<f32> {
    return m.transform_point(p);
}

/// Transform a vector, pretending that it's a point..
#[inline(always)]
pub fn transform_vec_as_point(m: &Matrix4<f32>, v: &Vector3<f32>) -> Point3<f32> {
    let p: Point3<f32> = (*v).into();
    return m.transform_point(&p);
}

