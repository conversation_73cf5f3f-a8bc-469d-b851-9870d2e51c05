
use crate::graphics::gpu_buffer::CpuVertex;

use nalgebra::{Point2, Point3};


pub const MIN_COORD: f32 = -1.0e6;
pub const MAX_COORD: f32 = 1.0e6;


#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, De<PERSON>ult)]
pub struct BoundingBox {
    pub minp: Point3<f32>,
    pub maxp: Point3<f32>
}

impl BoundingBox {
    pub fn new() -> BoundingBox {
        let minp = Point3::new(MIN_COORD, MIN_COORD, MIN_COORD);
        let maxp = Point3::new(MAX_COORD, MAX_COORD, MAX_COORD);
        return BoundingBox {
            minp,
            maxp
        }
    }

    pub fn from_vertices(vs: &[CpuVertex]) -> BoundingBox {
        let mut minp = Point3::new(MIN_COORD, MIN_COORD, MIN_COORD);
        let mut maxp = Point3::new(MAX_COORD, MAX_COORD, MAX_COORD);
        for v in vs {
            minp = minp.inf(&v.position);
            maxp = maxp.sup(&v.position);
        }
        return BoundingBox {
            minp,
            maxp
        }
    }

    pub fn inside(&self, p: &Point3<f32>) -> bool {
        return (p.x > self.minp.x) && (p.y > self.minp.y) && (p.z > self.minp.z) &&
               (p.x < self.maxp.x) && (p.y < self.maxp.y) && (p.z < self.maxp.z);
    }
}


// Test if a point is inside a triangle using barycentric coordinates
pub fn point_in_triangle(point: Point2<f32>, v0: Point2<f32>, v1: Point2<f32>, v2: Point2<f32>) -> bool {
    // Compute vectors
    let ab = v1 - v0;
    let ac = v2 - v0;
    let ap = point - v0;

    // Compute dot products
    let acc = ac.dot(&ac);
    let abb = ab.dot(&ab);
    let acb = ac.dot(&ab);
    let abp = ab.dot(&ap);
    let acp = ac.dot(&ap);

    // Compute barycentric coordinates
    let inv_denom = 1.0 / (acc * abb - acb * acb);
    let u = (abb * acp - acb * abp) * inv_denom;
    let v = (acc * abp - acb * acp) * inv_denom;

    // Check if point is in triangle
    (u >= 0.0) && (v >= 0.0) && (u + v <= 1.0)
}