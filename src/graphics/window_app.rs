
use std::sync::Arc;

use winit::{
    application::ApplicationHandler,
    dpi::{PhysicalPosition, PhysicalSize},
    event::{ElementState, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>c<PERSON><PERSON>el<PERSON>, WindowEvent},
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    window::{Window, WindowId},
};

use crate::graphics::{
    camera::CameraState,
    window::WindowState,
};


/// The SceneState holds a Camera, along with any other scene data.
/// It must implement a render() method to render the scene.
pub trait SceneState {
    // Create a new SceneState.
    fn new(wstate: &WindowState) -> Self;

    // Get the current state of the camera.
    fn get_camera_state(&mut self) -> &mut CameraState;

    // Prepare the scene for rendering, e.g. transfer any buffer data to GPU.
    // Called prior to render().
    fn prepare_to_render(&mut self, queue: &mut wgpu::Queue);

    // Called to render the scene.
    fn render(&self, render_pass: &mut wgpu::RenderPass);

    /// Called when a mouse button is pressed or released.
    /// button: which mouse button, state: pressed or released
    /// x and y are in normalized device coordinates (-1 to 1).
    fn mouse_button(&mut self, _button: MouseButton, _state: ElementState, _x: f32, _y: f32) {}

    /// Called when the mouse cursor moves within the window.
    /// x and y are in normalized device coordinates (-1 to 1).
    fn mouse_moved(&mut self, _x: f32, _y: f32) {}

    /// Called when the mouse is dragged with the left mouse button.
    fn mouse_dragged(&mut self, x: f32, y: f32, old_x: f32, old_y: f32) {
        let dx = x - old_x;
        let dy = y - old_y;

        // Pan the camera by moving its position
        let camera_state = self.get_camera_state();
        let pan_speed = 1.0 / camera_state.camera.scale;   // Adjust pan speed based on zoom level
        camera_state.camera.eye_position.x -= dx * pan_speed;
        camera_state.camera.eye_position.y -= dy * pan_speed;
        camera_state.camera.update_projection_matrices();
    }

    /// Called when the mouse wheel is scrolled.
    /// delta is positive for scrolling up (zoom in), negative for scrolling down (zoom out).
    fn mouse_wheel_scrolled(&mut self, delta: f32) {
        // Each click of the mouse wheel zooms by 10%
        // Positive delta = zoom in (scale up), negative delta = zoom out (scale down)
        let camera_state = self.get_camera_state();
        let zoom_factor: f32 = if delta > 0.0 { 1.1 } else { 1.0 / 1.1 };
        camera_state.camera.scale *= zoom_factor;
        camera_state.camera.update_projection_matrices();
    }
}


/// WindowedApp implements an event loop for an application that opens a single window.
/// It captures events, and forwards them to the window, camera, or scene.
pub struct WindowedApp<ST: SceneState> {
    pub window_state: Option<WindowState>,
    pub scene_state: Option<ST>,
    is_dragging: bool,                    // True when dragging the mouse
    last_mouse_pos: Option<(f32, f32)>,   // Used for mouse drag event; normalized device coordinates.
}


impl<ST: SceneState> Default for WindowedApp<ST> {
    fn default() -> Self {
        return Self {
            window_state: None,
            scene_state: None,
            is_dragging: false,
            last_mouse_pos: None,
        }
    }
}


impl<ST: SceneState> WindowedApp<ST> {
    /// Redraw the scene.
    fn redraw(&mut self) {
        let wstate = self.window_state.as_mut().unwrap();
        let scene_state = self.scene_state.as_mut().unwrap();

        // Do any pre-rendering preparation, such as transferring buffer data to GPU.
        scene_state.prepare_to_render(&mut wstate.queue);

        // Render the scene.
        let render_fn = |render_pass: &mut wgpu::RenderPass| {
            scene_state.render(render_pass);
        };
        wstate.render(render_fn);
        // Emits a new redraw requested event.
        wstate.window.request_redraw();
    }

    /// Resize the window surface.
    fn resized(&mut self, size: PhysicalSize<u32>) {
        let wstate = self.window_state.as_mut().unwrap();
        let scene_state = self.scene_state.as_mut().unwrap();

        // Reconfigures the size of the surface. We do not re-render
        // here as this event is always followed up by redraw request.
        wstate.resize(size);

        let camera_state = scene_state.get_camera_state();
        camera_state.set_aspect_ratio(size.width, size.height);
        camera_state.update_camera_buffer(&mut wstate.queue);
    }

    /// Handle a mouse moved event.
    fn mouse_moved(&mut self, position: PhysicalPosition<f64>) {
        let wstate = self.window_state.as_mut().unwrap();
        let scene_state = self.scene_state.as_mut().unwrap();

        // Convert screen coordinates to normalized device coordinates.
        // Flip the y axis.
        let x = (position.x / (wstate.size.width as f64) * 2.0 - 1.0) as f32;
        let y = (position.y / (wstate.size.height as f64) * -2.0 + 1.0) as f32;

        if self.is_dragging {
            // Forward event to the SceneState; default behavior is to pan the camera.
            let (oldx, oldy) = self.last_mouse_pos.unwrap();
            scene_state.mouse_dragged(x, y, oldx, oldy);
        } else {
            // Forward event to the SceneState.
            scene_state.mouse_moved(x, y);
        }
        // Update last mouse position
        self.last_mouse_pos = Some((x, y));

        // Update the camera buffer on GPU -- if we happened to pan the camera.
        let camera_state = scene_state.get_camera_state();
        camera_state.update_camera_buffer(&mut wstate.queue);
    }

    /// Handle a mouse button event.
    fn mouse_button(&mut self, button: MouseButton, state: ElementState) {
        let scene_state = self.scene_state.as_mut().unwrap();

        // Get mouse coordinates of the button event.
        let x;
        let y;
        match self.last_mouse_pos {
            None => {
                return;
            }
            Some((ox, oy)) => {
                x = ox;
                y = oy;
            }
        }

        // Send drag events when the left mouse button is pressed.
        if button == MouseButton::Left {
            match state {
                ElementState::Pressed => {
                    self.is_dragging = true;
                }
                ElementState::Released => {
                    self.is_dragging = false;
                }
            }
        }
        // Forward mouse button event to the SceneState for any additional handling.
        scene_state.mouse_button(button, state, x, y);
    }

    /// Handle a mouse wheel scroll event.
    fn mouse_wheel_scrolled(&mut self, delta: f32) {
        let wstate = self.window_state.as_mut().unwrap();
        let scene_state = self.scene_state.as_mut().unwrap();

        // Forward event to the SceneState; default behavior is to zoom the camera.
        scene_state.mouse_wheel_scrolled(delta);

        // Update the camera buffer on GPU.
        let camera_state = scene_state.get_camera_state();
        camera_state.update_camera_buffer(&mut wstate.queue);
    }
}


impl<ST: SceneState> ApplicationHandler for WindowedApp<ST> {
    /// Platform-agnostic entrypoint that runs on startup.
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        // Open a window.
        let window_attributes = Window::default_attributes()
            .with_inner_size(winit::dpi::PhysicalSize::new(1200, 1200));
        let window = Arc::new(
            event_loop
                .create_window(window_attributes)
                .unwrap(),
        );
        // Create the WindowState, which sets everything up.
        let wstate = pollster::block_on(WindowState::new(window.clone()));

        // Create the renderable object, whatever it happens to be.
        let scene_state = ST::new(&wstate);

        self.window_state = Some(wstate);
        self.scene_state = Some(scene_state);

        window.request_redraw();
    }

    /// Handle a window event.
    fn window_event(&mut self, event_loop: &ActiveEventLoop, _id: WindowId, event: WindowEvent) {
        // Handle events for the window that we opened.
        match event {
            WindowEvent::CloseRequested => {
                println!("The close button was pressed; stopping");
                event_loop.exit();
            }
            WindowEvent::RedrawRequested => {
                self.redraw();
            }
            WindowEvent::Resized(size) => {
                self.resized(size);
            }
            WindowEvent::CursorMoved { position, .. } => {
                self.mouse_moved(position)
            }
            WindowEvent::MouseInput { button, state, .. } => {
                self.mouse_button(button, state);
            }
            WindowEvent::MouseWheel { delta, .. } => {
                match delta {
                    MouseScrollDelta::LineDelta(_x, y) => {
                        // Each line scroll represents one "click" of the mouse wheel
                        // Positive y means scroll up (zoom in), negative means scroll down (zoom out)
                        self.mouse_wheel_scrolled(y);
                    }
                    MouseScrollDelta::PixelDelta(pos) => {
                        // For pixel-based scrolling, normalize to approximate line scrolling
                        // Typical mouse wheel gives about 120 pixels per "click"
                        let normalized_y = pos.y as f32 / 120.0;
                        self.mouse_wheel_scrolled(normalized_y);
                    }
                }
            }
            _ => (),
        }
    }
}


pub fn run_application<RT: SceneState>() {
    // wgpu uses `log` for all of our logging, so we initialize a logger with the `env_logger` crate.
    // To change the log level, set the `RUST_LOG` environment variable. See the `env_logger`
    // documentation for more information.
    env_logger::init();

    // Start running an event loop get events from the system.
    let event_loop = EventLoop::new().unwrap();

    // Option 1:
    // When the current loop iteration finishes, immediately begin a new
    // iteration regardless of whether or not new events are available to
    // process. Preferred for applications that want to render as fast as
    // possible, like games.
    event_loop.set_control_flow(ControlFlow::Poll);

    // Option 2:
    // When the current loop iteration finishes, suspend the thread until
    // another event arrives. Helps keeping CPU utilization low if nothing
    // is happening, which is preferred if the application might be idling in
    // the background.
    // event_loop.set_control_flow(ControlFlow::Wait);

    // Create the application, which opens a window, and start passing events to it.
    let mut windowed_app = WindowedApp::<RT>::default();
    event_loop.run_app(&mut windowed_app).unwrap();
}
