
use std::cell::{<PERSON>f<PERSON><PERSON>, <PERSON>f, RefMut, UnsafeCell};
use std::marker::PhantomData;


/// A slot for an item in the storage pool.
pub struct StoragePoolSlot<T> {
    pub id: usize,
    pub valid: bool,
    item: UnsafeCell<T>,
}

impl<T> StoragePoolSlot<T> {
    fn new(id: usize, value: T) -> StoragePoolSlot<T> {
        return StoragePoolSlot {
            id,
            valid: true,
            item: UnsafeCell::new(value),
        }
    }

    /// Unsafe version of RefCell.borrow().
    #[inline(always)]
    pub fn borrow(&self) -> &T {
        unsafe { return &*self.item.get(); }
    }

    /// Unsafe version of RefCell.borrow_mut().
    #[inline(always)]
    pub fn borrow_mut(&self) -> &mut T {
        unsafe { return &mut *self.item.get(); }
    }
}


/// A handle to an item in a storage pool.
#[derive(Default, Debug)]
pub struct Handle<T> {
    index: usize,
    data: PhantomData<T>
}

impl<T> Handle<T> {
    pub fn new(i: usize) -> Handle<T> {
        return Handle {
            index: i,
            data: PhantomData
        }
    }
}

// Cannot derive(Clone), because it breaks if T does not implement clone.
impl<T> Clone for Handle<T> {
    fn clone(&self) -> Handle<T> {
        return *self;
    }
}

// Allow bitwise copies of handles.
impl<T> Copy for Handle<T> {}


/// A StoragePool maintains a block of memory, where items can be allocated and deleted.
pub struct StoragePool<T> {
    pub items: Vec<StoragePoolSlot<T>>,
    free_list: Vec<Handle<T>>,
}

impl<T> StoragePool<T> {
    /// Create a new empty StoragePool.
    pub fn new() -> StoragePool<T> {
        let items = Vec::<StoragePoolSlot<T>>::new();
        let free_list = Vec::<Handle<T>>::new();
        return StoragePool {
            items,
            free_list
        };
    }

    /// Returns the number of items in the pool.
    #[inline(always)]
    pub fn len(&self) -> usize {
        return self.items.len() - self.free_list.len();
    }

    /// Get a reference from a handle.
    #[inline(always)]
    pub fn get(&self, h: Handle<T>) -> &T {
        return self.items[h.index].borrow();
    }

    /// Get a mutable reference from a handle.
    #[inline(always)]
    pub fn get_mut(&self, h: Handle<T>) -> &mut T {
        return self.items[h.index].borrow_mut();
    }

    /// Allocate a new item in the pool.
    pub fn alloc(&mut self, value: T) -> Handle<T> {
        if self.free_list.len() > 0 {
            let h = self.free_list.pop().unwrap();
            self.items[h.index] = StoragePoolSlot::new(h.index, value);
            return h;
        }
        let hi = self.items.len();
        self.items.push(StoragePoolSlot::new(hi, value));
        return Handle::new(hi);
    }

    /// Delete an item from the pool.
    #[inline]
    pub fn delete(&mut self, h: Handle<T>) {
        // The destructor is not called yet, but when the cell is allocated to something else.
        self.free_list.push(h);
        self.items[h.index].valid = false
    }
}
