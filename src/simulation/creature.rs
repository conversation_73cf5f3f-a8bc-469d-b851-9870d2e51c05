
use crate::{
    graphics::{
        scene::{Body, RenderableBody, get_view_matrix, transform_vec_as_point},
    },
    simulation::{
        storage::StoragePoolSlot,
    }
};

use crate::simulation::storage::{Handle, StoragePool};


use nalgebra::{
    Matrix4, Point3, Rotation3, Vector3
};


#[derive(<PERSON><PERSON>, Debug, Default)]
pub struct Cell {
    pub body: Body,
    pub joints: Vec<Joint>,            // Joints for which this cell is the parent.
}

impl Cell {
    #[inline]
    fn position(&self) -> &Point3<f32> {
        return &self.body.position;
    }

    #[inline]
    fn rotation(&self) -> f32 {
        return self.body.rotation;
    }

    #[inline]
    fn scale(&self) -> f32 {
        return self.body.scale;
    }

    #[inline]
    fn view_matrix(&self) -> &Matrix4<f32> {
        return &self.body.view_matrix;
    }
}


#[derive(Clone, Debug, Default)]
pub struct Joint {
    pub parent: Handle<Cell>,
    pub child:  Handle<Cell>,
    pub parent_offset: Vector3<f32>,   // The offset of this joint from the parent cell position in cell coords.
    pub child_offset:  Vector3<f32>,   // The offset of this joint from the child cell position in cell coords.
    pub angle:  f32                   // The angle of the child, relative to the parent.
}


#[derive(Debug, Default)]
pub struct Creature {
    pub root_cell: Handle<Cell>,
}


pub struct World {
    pub creatures: StoragePool<Creature>,
    pub cells: StoragePool<Cell>,
}

impl World {
    /// Create a new empty world.
    pub fn new() -> World {
        return World {
            creatures: StoragePool::new(),
            cells: StoragePool::new(),
        }
    }

    /// Update the position of all creatures and cells in the world.
    pub fn update(&self) {
        for cr in &self.creatures.items {
            if !cr.valid {
                continue;
            }
            self.update_creature_cells(cr.borrow().root_cell);
        }
    }

    /// Update the size and position of all cells in a creature, starting from the root.
    fn update_creature_cells(&self, cell_h: Handle<Cell>) {
        let cell = self.cells.get_mut(cell_h);

        cell.body.view_matrix = get_view_matrix(cell.position(), cell.rotation(), cell.scale());
        cell.body.transform_vertices();

        for j in &cell.joints {
            {
                let child_cell = self.cells.get_mut(j.child);

                // Compute child rotation.
                child_cell.body.rotation = cell.rotation() + j.angle;

                // Compute child position.
                // j_pos = joint position in world coords, calculated from parent.
                let j_pos: Point3<f32> = transform_vec_as_point(cell.view_matrix(), &j.parent_offset);
                let rot = Rotation3::from_axis_angle(
                    &Vector3::z_axis(), child_cell.rotation()
                );
                let j_child_offset: Vector3<f32> = (rot * j.child_offset) * child_cell.scale();
                child_cell.body.position = j_pos - j_child_offset;
            }
            self.update_creature_cells(j.child);
        }
    }

    #[inline]
    pub fn add_cell(&mut self, cell: Cell) -> Handle<Cell> {
        return self.cells.alloc(cell);
    }

    #[inline]
    pub fn add_creature(&mut self, creature: Creature) -> Handle<Creature> {
        return self.creatures.alloc(creature);
    }
}


// Allow a Scene to iterate over StoragePool<Cell>
impl RenderableBody for StoragePoolSlot<Cell> {
    #[inline(always)]
    fn is_visible(&self) -> bool {
        return self.valid;
    }

    #[inline(always)]
    fn get_body(&self) -> &Body {
        return &self.borrow().body;
    }

    #[inline(always)]
    fn get_body_mut(&mut self) -> &mut Body {
        return &mut self.borrow_mut().body;
    }
}


// Hack to access creatures, without losing the mutable reference to self.
// Necessary for code which iterates over creatures, or recursive methods.
// #[inline]
// pub fn get_mutable_creatures(&mut self) -> (&mut StoragePool<Creature>, &mut Self) {
//     unsafe {
//         let s = self as *mut Self;
//         let creatures = &mut (*s).creatures;
//         return (creatures, &mut *s);
//     }
// }
// Hack to access cells, without losing the mutable reference to self.
// Necessary for code which iterates over cells, or recursive methods.
// #[inline]
// pub fn get_mutable_cells(&mut self) -> (&mut StoragePool<Cell>, &mut Self) {
//     unsafe {
//         let s = self as *mut Self;
//         let cells = &mut (*s).cells;
//         return (cells, &mut *s);
//     }
// }