
use rustwin::graphics::{
    camera::CameraState,
    gpu_buffer::CpuVertex,
    render::{TriangleList, triangle_render_pipeline},
    window::WindowState,
    window_app::{SceneState, run_application},
};

use nalgebra::Point3;


const TRIANGLE: &[CpuVertex] = &[
    CpuVertex {
        position: Point3::<f32>::new(0.0, 0.5, 0.0),
        color: Point3::new(1.0, 0.0, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(-0.5, -0.5, 0.0),
        color: Point3::<f32>::new(0.0, 1.0, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(0.5, -0.5, 0.0),
        color: Point3::<f32>::new(0.0, 0.0, 1.0)
    },
];


struct MyTriangleRenderer {
    camera_state: CameraState,
    render_pipeline: wgpu::RenderPipeline,
    triangles: TriangleList,
}

impl SceneState for MyTriangleRenderer {
    fn new(wstate: &WindowState) -> Self {
        let mut camera_state = CameraState::new(&wstate.device);
        camera_state.set_aspect_ratio(wstate.size.width, wstate.size.height);

        let render_pipeline = triangle_render_pipeline(
            wstate,
            &camera_state
        );
        let mut triangles = TriangleList::new(&wstate.device, 16, 16);
        for v in TRIANGLE {
            triangles.add(v.clone());
        }

        return Self {
            camera_state,
            render_pipeline,
            triangles,
        }
    }

    fn get_camera_state(&mut self) -> &mut CameraState {
        return &mut self.camera_state;
    }

    fn prepare_to_render(&mut self, queue: &mut wgpu::Queue) {
        // Copy triangle data to the GPU.
        self.triangles.update_buffer(queue);
    }

    fn render(&self, render_pass: &mut wgpu::RenderPass) {
        render_pass.set_pipeline(&self.render_pipeline);
        render_pass.set_bind_group(0, &self.camera_state.bind_group, &[]);
        self.triangles.render(render_pass);
    }
}


fn main() {
    run_application::<MyTriangleRenderer>();
}
