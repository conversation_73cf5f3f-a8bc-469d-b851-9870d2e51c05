
use rand::Rng;

use rustwin::graphics::{
    camera::CameraState,
    gpu_buffer::CpuVertex,
    render::triangle_render_pipeline,
    scene::{Body, Scene},
    window::WindowState,
    window_app::{SceneState, run_application},
};

use nalgebra::{Point2, Point3};


const TRIANGLE: &[CpuVertex] = &[
    CpuVertex {
        position: Point3::<f32>::new(0.0, 0.1, 0.0),
        color: Point3::new(1.0, 0.0, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(-0.1, -0.1, 0.0),
        color: Point3::<f32>::new(0.0, 1.0, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(0.1, -0.1, 0.0),
        color: Point3::<f32>::new(0.0, 0.0, 1.0)
    },
];

const SQUARE: &[CpuVertex] = &[
    CpuVertex {
        position: Point3::<f32>::new(-0.1, -0.1, 0.0),
        color: Point3::new(1.0, 0.0, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(-0.1, 0.1, 0.0),
        color: Point3::<f32>::new(0.0, 1.0, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(0.1, 0.1, 0.0),
        color: Point3::<f32>::new(0.0, 0.0, 1.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(0.1, -0.1, 0.0),
        color: Point3::<f32>::new(0.0, 1.0, 0.0)
    },
];


fn make_polygon(p: Point3<f32>) -> Body {
    let mut rng = rand::rng();

    // Random number of sides between 3 and 6
    let num_sides = rng.random_range(3..=7);
    if num_sides == 3 {
        return Body::new(p, &TRIANGLE, &[0, 1, 2]);
    }
    else if num_sides == 4 {
        return Body::new(p, &SQUARE, &[0, 2, 1, 0, 3, 2]);
    }

    let radius = 0.1f32;
    let mut vertices = Vec::new();
    let mut indices = Vec::new();

    // Center vertex with random color.
    vertices.push(CpuVertex {
        position: Point3::new(0.0, 0.0, 0.0), // Relative to center
        color: Point3::new(rng.random(), rng.random(), rng.random())
    });

    // Generate vertices around the circle.
    for i in 0..num_sides {
        let angle = 2.0 * std::f32::consts::PI * (i as f32) / (num_sides as f32);
        let x = radius * angle.cos();
        let y = radius * angle.sin();

        vertices.push(CpuVertex {
            position: Point3::new(x, y, 0.0),
            color: Point3::new(rng.random(), rng.random(), rng.random())
        });
    }

    // Generate triangular faces from center to each edge.
    for i in 0..num_sides {
        let next_i = (i + 1) % num_sides;
        indices.extend_from_slice(&[0, (i + 1) as u16, (next_i + 1) as u16]);
    }

    return Body::new(p, &vertices, &indices);
}


struct MyTriangleRenderer {
    camera_state: CameraState,
    bodies: Vec<Body>,
    scene: Scene<Body>,
    render_pipeline: wgpu::RenderPipeline,
    current_selector_point: Option<Point2<f32>>,
}

impl SceneState for MyTriangleRenderer {
    fn new(wstate: &WindowState) -> Self {
        let mut camera_state = CameraState::new(&wstate.device);
        camera_state.set_aspect_ratio(wstate.size.width, wstate.size.height);

        let mut bodies = Vec::new();
        for i in -2..=2 {
            for j in -2..=2 {
                let p = Point3::<f32>::new(
                    (i as f32)*0.3,
                    (j as f32)*0.3,
                    0.5
                );
                let body = make_polygon(p);
                bodies.push(body);
            }
        }

        let scene = Scene::new(&wstate.device);

        let render_pipeline = triangle_render_pipeline(
            wstate,
            &camera_state
        );

        return Self {
            camera_state,
            bodies,
            scene,
            render_pipeline,
            current_selector_point: None,
        }
    }

    fn get_camera_state(&mut self) -> &mut CameraState {
        return &mut self.camera_state;
    }

    fn prepare_to_render(&mut self, queue: &mut wgpu::Queue) {
        for b in &mut self.bodies {
            b.rotation += 0.01;
            b.update_view_matrix();
        }
        match self.current_selector_point {
            Some(p) => self.scene.select(self.bodies.as_mut_slice(), p),
            None => {}
        }
        self.scene.update_buffer(self.bodies.as_mut_slice(), queue);
    }

    fn render(&self, render_pass: &mut wgpu::RenderPass) {
        render_pass.set_pipeline(&self.render_pipeline);
        render_pass.set_bind_group(0, &self.camera_state.bind_group, &[]);
        self.scene.render(render_pass);
    }

    fn mouse_moved(&mut self, x: f32, y: f32) {
        // Convert screen coordinates to world coordinates using stored window dimensions
        let world_point =
            self.camera_state.camera.screen_to_world(x as f32, y as f32).xy();
        self.current_selector_point = Some(world_point);
        // println!("mouse_moved {:?}", world_point);
    }
}


fn main() {
    run_application::<MyTriangleRenderer>();
}
